{"skeleton": {"hash": "vGZQx+B25uU", "spine": "4.2.40", "x": -117.5, "y": -118.5, "width": 235, "height": 237, "images": "./Images/Black_hole2/", "audio": "./audio"}, "bones": [{"name": "root"}, {"name": "cntr", "parent": "root"}, {"name": "<PERSON><PERSON><PERSON>", "parent": "cntr", "icon": "rotate"}, {"name": "Circles", "parent": "cntr"}, {"name": "Circle_1", "parent": "Circles", "x": -2.6, "y": -3.53, "color": "81da47ff", "icon": "circle"}, {"name": "Circle_edge1", "parent": "Circle_1"}, {"name": "Circle_2", "parent": "Circles", "x": -2.6, "y": -3.53, "color": "81da47ff", "icon": "circle"}, {"name": "Circle_edge2", "parent": "Circle_2"}, {"name": "Circle_3", "parent": "Circles", "x": -2.6, "y": -3.53, "color": "81da47ff", "icon": "circle"}, {"name": "Circle_edge3", "parent": "Circle_3"}, {"name": "Circle_0", "parent": "Circles", "x": -2.6, "y": -3.53, "color": "81da47ff", "icon": "circle"}, {"name": "Circle_edge0", "parent": "Circle_0"}], "slots": [{"name": "<PERSON><PERSON><PERSON>", "bone": "<PERSON><PERSON><PERSON>", "attachment": "<PERSON><PERSON><PERSON>"}, {"name": "Circle3", "bone": "Circle_3", "color": "431377ff", "attachment": "Levels 1"}, {"name": "Circle_edge3", "bone": "Circle_3", "color": "a43dc4ff", "attachment": "Levels 2"}, {"name": "Circle2", "bone": "Circle_2", "color": "431377ff", "attachment": "Levels 1"}, {"name": "Circle_edge2", "bone": "Circle_2", "color": "a43dc4ff", "attachment": "Levels 2"}, {"name": "Circle1", "bone": "Circle_1", "color": "431377ff", "attachment": "Levels 1"}, {"name": "Circle_edge1", "bone": "Circle_1", "color": "a43dc4ff", "attachment": "Levels 2"}, {"name": "Circle0", "bone": "Circle_0", "color": "431377ff", "attachment": "Levels 1"}, {"name": "Circle_edge0", "bone": "Circle_0", "color": "a43dc4ff", "attachment": "Levels 2"}], "skins": [{"name": "default", "attachments": {"Circle0": {"Levels 1": {"x": 0.1, "y": 0.03, "width": 136, "height": 136}}, "Circle1": {"Levels 1": {"x": 0.1, "y": 0.03, "width": 136, "height": 136}}, "Circle2": {"Levels 1": {"x": 0.1, "y": 0.03, "width": 136, "height": 136}}, "Circle3": {"Levels 1": {"x": 0.1, "y": 0.03, "width": 136, "height": 136}}, "Circle_edge0": {"Levels 2": {"type": "mesh", "uvs": [0.7531, 0.06089, 0.86842, 0.15927, 0.95467, 0.28335, 0.99304, 0.40052, 0.9931, 0.58652, 0.95405, 0.72019, 0.86666, 0.84281, 0.75827, 0.93182, 0.59996, 0.9933, 0.38583, 0.9932, 0.23656, 0.93382, 0.13204, 0.84454, 0.04965, 0.72703, 0.00558, 0.59211, 0.00458, 0.42515, 0.05128, 0.27313, 0.13891, 0.14742, 0.25858, 0.05528, 0.40291, 0.00683, 0.60279, 0.00689, 0.50052, 0.49883, 0.06027, 0.43397, 0.06179, 0.58657, 0.10376, 0.69873, 0.17319, 0.80021, 0.25712, 0.87193, 0.40056, 0.93068, 0.58902, 0.93373, 0.72713, 0.88109, 0.82326, 0.80479, 0.89575, 0.70484, 0.93695, 0.58199, 0.93542, 0.41261, 0.89956, 0.3096, 0.82708, 0.19744, 0.72178, 0.11504, 0.59131, 0.06468, 0.41506, 0.06315, 0.29222, 0.10741, 0.18311, 0.18752, 0.10071, 0.30121], "triangles": [36, 37, 19, 35, 36, 19, 37, 38, 18, 38, 39, 17, 34, 35, 1, 40, 15, 39, 33, 34, 2, 32, 33, 2, 21, 14, 40, 20, 37, 36, 20, 36, 35, 20, 35, 34, 20, 34, 33, 20, 33, 32, 38, 37, 20, 39, 38, 20, 40, 39, 20, 21, 40, 20, 4, 31, 32, 20, 32, 31, 22, 21, 20, 13, 21, 22, 23, 22, 20, 30, 20, 31, 5, 30, 31, 12, 22, 23, 24, 23, 20, 29, 20, 30, 6, 29, 30, 24, 12, 23, 25, 24, 20, 20, 26, 25, 29, 28, 20, 20, 27, 26, 7, 28, 29, 28, 27, 20, 25, 11, 24, 10, 25, 26, 7, 27, 28, 26, 27, 8, 37, 18, 19, 38, 17, 18, 0, 35, 19, 39, 16, 17, 35, 0, 1, 15, 16, 39, 34, 1, 2, 3, 32, 2, 14, 15, 40, 4, 32, 3, 13, 14, 21, 4, 5, 31, 12, 13, 22, 6, 30, 5, 11, 12, 24, 7, 29, 6, 10, 11, 25, 9, 10, 26, 7, 8, 27, 9, 26, 8], "vertices": [1, 10, 36.04, 62.38, 1, 1, 10, 52.42, 48.41, 1, 1, 10, 64.67, 30.79, 1, 1, 10, 70.12, 14.15, 1, 1, 10, 70.12, -12.26, 1, 1, 10, 64.58, -31.24, 1, 1, 10, 52.17, -48.65, 1, 1, 10, 36.78, -61.29, 1, 1, 10, 14.3, -70.02, 1, 1, 10, -16.11, -70.01, 1, 1, 10, -37.3, -61.58, 1, 1, 10, -52.15, -48.9, 1, 1, 10, -63.85, -32.21, 1, 1, 10, -70.1, -13.05, 1, 1, 10, -70.25, 10.66, 1, 1, 10, -63.61, 32.24, 1, 1, 10, -51.17, 50.09, 1, 1, 10, -34.18, 63.18, 1, 1, 10, -13.68, 70.06, 1, 1, 10, 14.7, 70.05, 1, 1, 11, 0.18, 0.19, 1, 1, 11, -62.34, 9.4, 1, 1, 11, -62.12, -12.27, 1, 1, 11, -56.16, -28.19, 1, 1, 11, -46.3, -42.6, 1, 1, 11, -34.38, -52.79, 1, 1, 11, -14.02, -61.13, 1, 1, 11, 12.75, -61.56, 1, 1, 11, 32.36, -54.09, 1, 1, 11, 46.01, -43.25, 1, 1, 11, 56.3, -29.06, 1, 1, 11, 62.15, -11.62, 1, 1, 11, 61.93, 12.44, 1, 1, 11, 56.84, 27.06, 1, 1, 11, 46.55, 42.99, 1, 1, 11, 31.6, 54.69, 1, 1, 11, 13.07, 61.84, 1, 1, 11, -11.96, 62.06, 1, 1, 11, -29.4, 55.77, 1, 1, 11, -44.89, 44.4, 1, 1, 11, -56.6, 28.26, 1], "hull": 20, "edges": [0, 38, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 24, 26, 26, 28, 28, 30, 34, 36, 36, 38, 30, 32, 32, 34, 20, 22, 22, 24], "width": 142, "height": 142}}, "Circle_edge1": {"Levels 2": {"type": "mesh", "uvs": [0.7531, 0.06089, 0.86842, 0.15927, 0.95467, 0.28335, 0.99304, 0.40052, 0.9931, 0.58652, 0.95405, 0.72019, 0.86666, 0.84281, 0.75827, 0.93182, 0.59996, 0.9933, 0.38583, 0.9932, 0.23656, 0.93382, 0.13204, 0.84454, 0.04965, 0.72703, 0.00558, 0.59211, 0.00458, 0.42515, 0.05128, 0.27313, 0.13891, 0.14742, 0.25858, 0.05528, 0.40291, 0.00683, 0.60279, 0.00689, 0.50052, 0.49883, 0.06027, 0.43397, 0.06179, 0.58657, 0.10376, 0.69873, 0.17319, 0.80021, 0.25712, 0.87193, 0.40056, 0.93068, 0.58902, 0.93373, 0.72713, 0.88109, 0.82326, 0.80479, 0.89575, 0.70484, 0.93695, 0.58199, 0.93542, 0.41261, 0.89956, 0.3096, 0.82708, 0.19744, 0.72178, 0.11504, 0.59131, 0.06468, 0.41506, 0.06315, 0.29222, 0.10741, 0.18311, 0.18752, 0.10071, 0.30121], "triangles": [36, 37, 19, 35, 36, 19, 37, 38, 18, 38, 39, 17, 34, 35, 1, 40, 15, 39, 33, 34, 2, 32, 33, 2, 21, 14, 40, 20, 37, 36, 20, 36, 35, 20, 35, 34, 20, 34, 33, 20, 33, 32, 38, 37, 20, 39, 38, 20, 40, 39, 20, 21, 40, 20, 4, 31, 32, 20, 32, 31, 22, 21, 20, 13, 21, 22, 23, 22, 20, 30, 20, 31, 5, 30, 31, 12, 22, 23, 24, 23, 20, 29, 20, 30, 6, 29, 30, 24, 12, 23, 25, 24, 20, 20, 26, 25, 29, 28, 20, 20, 27, 26, 7, 28, 29, 28, 27, 20, 25, 11, 24, 10, 25, 26, 7, 27, 28, 26, 27, 8, 37, 18, 19, 38, 17, 18, 0, 35, 19, 39, 16, 17, 35, 0, 1, 15, 16, 39, 34, 1, 2, 3, 32, 2, 14, 15, 40, 4, 32, 3, 13, 14, 21, 4, 5, 31, 12, 13, 22, 6, 30, 5, 11, 12, 24, 7, 29, 6, 10, 11, 25, 9, 10, 26, 7, 8, 27, 9, 26, 8], "vertices": [1, 4, 36.04, 62.38, 1, 1, 4, 52.42, 48.41, 1, 1, 4, 64.67, 30.79, 1, 1, 4, 70.12, 14.15, 1, 1, 4, 70.12, -12.26, 1, 1, 4, 64.58, -31.24, 1, 1, 4, 52.17, -48.65, 1, 1, 4, 36.78, -61.29, 1, 1, 4, 14.3, -70.02, 1, 1, 4, -16.11, -70.01, 1, 1, 4, -37.3, -61.58, 1, 1, 4, -52.15, -48.9, 1, 1, 4, -63.85, -32.21, 1, 1, 4, -70.1, -13.05, 1, 1, 4, -70.25, 10.66, 1, 1, 4, -63.61, 32.24, 1, 1, 4, -51.17, 50.09, 1, 1, 4, -34.18, 63.18, 1, 1, 4, -13.68, 70.06, 1, 1, 4, 14.7, 70.05, 1, 1, 5, 0.18, 0.19, 1, 1, 5, -62.34, 9.4, 1, 1, 5, -62.12, -12.27, 1, 1, 5, -56.16, -28.19, 1, 1, 5, -46.3, -42.6, 1, 1, 5, -34.38, -52.79, 1, 1, 5, -14.02, -61.13, 1, 1, 5, 12.75, -61.56, 1, 1, 5, 32.36, -54.09, 1, 1, 5, 46.01, -43.25, 1, 1, 5, 56.3, -29.06, 1, 1, 5, 62.15, -11.62, 1, 1, 5, 61.93, 12.44, 1, 1, 5, 56.84, 27.06, 1, 1, 5, 46.55, 42.99, 1, 1, 5, 31.6, 54.69, 1, 1, 5, 13.07, 61.84, 1, 1, 5, -11.96, 62.06, 1, 1, 5, -29.4, 55.77, 1, 1, 5, -44.89, 44.4, 1, 1, 5, -56.6, 28.26, 1], "hull": 20, "edges": [0, 38, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 24, 26, 26, 28, 28, 30, 34, 36, 36, 38, 30, 32, 32, 34, 20, 22, 22, 24], "width": 142, "height": 142}}, "Circle_edge2": {"Levels 2": {"type": "mesh", "uvs": [0.7531, 0.06089, 0.86842, 0.15927, 0.95467, 0.28335, 0.99304, 0.40052, 0.9931, 0.58652, 0.95405, 0.72019, 0.86666, 0.84281, 0.75827, 0.93182, 0.59996, 0.9933, 0.38583, 0.9932, 0.23656, 0.93382, 0.13204, 0.84454, 0.04965, 0.72703, 0.00558, 0.59211, 0.00458, 0.42515, 0.05128, 0.27313, 0.13891, 0.14742, 0.25858, 0.05528, 0.40291, 0.00683, 0.60279, 0.00689, 0.50052, 0.49883, 0.06027, 0.43397, 0.06179, 0.58657, 0.10376, 0.69873, 0.17319, 0.80021, 0.25712, 0.87193, 0.40056, 0.93068, 0.58902, 0.93373, 0.72713, 0.88109, 0.82326, 0.80479, 0.89575, 0.70484, 0.93695, 0.58199, 0.93542, 0.41261, 0.89956, 0.3096, 0.82708, 0.19744, 0.72178, 0.11504, 0.59131, 0.06468, 0.41506, 0.06315, 0.29222, 0.10741, 0.18311, 0.18752, 0.10071, 0.30121], "triangles": [36, 37, 19, 35, 36, 19, 37, 38, 18, 38, 39, 17, 34, 35, 1, 40, 15, 39, 33, 34, 2, 32, 33, 2, 21, 14, 40, 20, 37, 36, 20, 36, 35, 20, 35, 34, 20, 34, 33, 20, 33, 32, 38, 37, 20, 39, 38, 20, 40, 39, 20, 21, 40, 20, 4, 31, 32, 20, 32, 31, 22, 21, 20, 13, 21, 22, 23, 22, 20, 30, 20, 31, 5, 30, 31, 12, 22, 23, 24, 23, 20, 29, 20, 30, 6, 29, 30, 24, 12, 23, 25, 24, 20, 20, 26, 25, 29, 28, 20, 20, 27, 26, 7, 28, 29, 28, 27, 20, 25, 11, 24, 10, 25, 26, 7, 27, 28, 26, 27, 8, 37, 18, 19, 38, 17, 18, 0, 35, 19, 39, 16, 17, 35, 0, 1, 15, 16, 39, 34, 1, 2, 3, 32, 2, 14, 15, 40, 4, 32, 3, 13, 14, 21, 4, 5, 31, 12, 13, 22, 6, 30, 5, 11, 12, 24, 7, 29, 6, 10, 11, 25, 9, 10, 26, 7, 8, 27, 9, 26, 8], "vertices": [1, 6, 36.04, 62.38, 1, 1, 6, 52.42, 48.41, 1, 1, 6, 64.67, 30.79, 1, 1, 6, 70.12, 14.15, 1, 1, 6, 70.12, -12.26, 1, 1, 6, 64.58, -31.24, 1, 1, 6, 52.17, -48.65, 1, 1, 6, 36.78, -61.29, 1, 1, 6, 14.3, -70.02, 1, 1, 6, -16.11, -70.01, 1, 1, 6, -37.3, -61.58, 1, 1, 6, -52.15, -48.9, 1, 1, 6, -63.85, -32.21, 1, 1, 6, -70.1, -13.05, 1, 1, 6, -70.25, 10.66, 1, 1, 6, -63.61, 32.24, 1, 1, 6, -51.17, 50.09, 1, 1, 6, -34.18, 63.18, 1, 1, 6, -13.68, 70.06, 1, 1, 6, 14.7, 70.05, 1, 1, 7, 0.18, 0.19, 1, 1, 7, -62.34, 9.4, 1, 1, 7, -62.12, -12.27, 1, 1, 7, -56.16, -28.19, 1, 1, 7, -46.3, -42.6, 1, 1, 7, -34.38, -52.79, 1, 1, 7, -14.02, -61.13, 1, 1, 7, 12.75, -61.56, 1, 1, 7, 32.36, -54.09, 1, 1, 7, 46.01, -43.25, 1, 1, 7, 56.3, -29.06, 1, 1, 7, 62.15, -11.62, 1, 1, 7, 61.93, 12.44, 1, 1, 7, 56.84, 27.06, 1, 1, 7, 46.55, 42.99, 1, 1, 7, 31.6, 54.69, 1, 1, 7, 13.07, 61.84, 1, 1, 7, -11.96, 62.06, 1, 1, 7, -29.4, 55.77, 1, 1, 7, -44.89, 44.4, 1, 1, 7, -56.6, 28.26, 1], "hull": 20, "edges": [0, 38, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 24, 26, 26, 28, 28, 30, 34, 36, 36, 38, 30, 32, 32, 34, 20, 22, 22, 24], "width": 142, "height": 142}}, "Circle_edge3": {"Levels 2": {"type": "mesh", "uvs": [0.7531, 0.06089, 0.86842, 0.15927, 0.95467, 0.28335, 0.99304, 0.40052, 0.9931, 0.58652, 0.95405, 0.72019, 0.86666, 0.84281, 0.75827, 0.93182, 0.59996, 0.9933, 0.38583, 0.9932, 0.23656, 0.93382, 0.13204, 0.84454, 0.04965, 0.72703, 0.00558, 0.59211, 0.00458, 0.42515, 0.05128, 0.27313, 0.13891, 0.14742, 0.25858, 0.05528, 0.40291, 0.00683, 0.60279, 0.00689, 0.50052, 0.49883, 0.06027, 0.43397, 0.06179, 0.58657, 0.10376, 0.69873, 0.17319, 0.80021, 0.25712, 0.87193, 0.40056, 0.93068, 0.58902, 0.93373, 0.72713, 0.88109, 0.82326, 0.80479, 0.89575, 0.70484, 0.93695, 0.58199, 0.93542, 0.41261, 0.89956, 0.3096, 0.82708, 0.19744, 0.72178, 0.11504, 0.59131, 0.06468, 0.41506, 0.06315, 0.29222, 0.10741, 0.18311, 0.18752, 0.10071, 0.30121], "triangles": [36, 37, 19, 35, 36, 19, 37, 38, 18, 38, 39, 17, 34, 35, 1, 40, 15, 39, 33, 34, 2, 32, 33, 2, 21, 14, 40, 20, 37, 36, 20, 36, 35, 20, 35, 34, 20, 34, 33, 20, 33, 32, 38, 37, 20, 39, 38, 20, 40, 39, 20, 21, 40, 20, 4, 31, 32, 20, 32, 31, 22, 21, 20, 13, 21, 22, 23, 22, 20, 30, 20, 31, 5, 30, 31, 12, 22, 23, 24, 23, 20, 29, 20, 30, 6, 29, 30, 24, 12, 23, 25, 24, 20, 20, 26, 25, 29, 28, 20, 20, 27, 26, 7, 28, 29, 28, 27, 20, 25, 11, 24, 10, 25, 26, 7, 27, 28, 26, 27, 8, 37, 18, 19, 38, 17, 18, 0, 35, 19, 39, 16, 17, 35, 0, 1, 15, 16, 39, 34, 1, 2, 3, 32, 2, 14, 15, 40, 4, 32, 3, 13, 14, 21, 4, 5, 31, 12, 13, 22, 6, 30, 5, 11, 12, 24, 7, 29, 6, 10, 11, 25, 9, 10, 26, 7, 8, 27, 9, 26, 8], "vertices": [1, 8, 36.04, 62.38, 1, 1, 8, 52.42, 48.41, 1, 1, 8, 64.67, 30.79, 1, 1, 8, 70.12, 14.15, 1, 1, 8, 70.12, -12.26, 1, 1, 8, 64.58, -31.24, 1, 1, 8, 52.17, -48.65, 1, 1, 8, 36.78, -61.29, 1, 1, 8, 14.3, -70.02, 1, 1, 8, -16.11, -70.01, 1, 1, 8, -37.3, -61.58, 1, 1, 8, -52.15, -48.9, 1, 1, 8, -63.85, -32.21, 1, 1, 8, -70.1, -13.05, 1, 1, 8, -70.25, 10.66, 1, 1, 8, -63.61, 32.24, 1, 1, 8, -51.17, 50.09, 1, 1, 8, -34.18, 63.18, 1, 1, 8, -13.68, 70.06, 1, 1, 8, 14.7, 70.05, 1, 1, 9, 0.18, 0.19, 1, 1, 9, -62.34, 9.4, 1, 1, 9, -62.12, -12.27, 1, 1, 9, -56.16, -28.19, 1, 1, 9, -46.3, -42.6, 1, 1, 9, -34.38, -52.79, 1, 1, 9, -14.02, -61.13, 1, 1, 9, 12.75, -61.56, 1, 1, 9, 32.36, -54.09, 1, 1, 9, 46.01, -43.25, 1, 1, 9, 56.3, -29.06, 1, 1, 9, 62.15, -11.62, 1, 1, 9, 61.93, 12.44, 1, 1, 9, 56.84, 27.06, 1, 1, 9, 46.55, 42.99, 1, 1, 9, 31.6, 54.69, 1, 1, 9, 13.07, 61.84, 1, 1, 9, -11.96, 62.06, 1, 1, 9, -29.4, 55.77, 1, 1, 9, -44.89, 44.4, 1, 1, 9, -56.6, 28.26, 1], "hull": 20, "edges": [0, 38, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 24, 26, 26, 28, 28, 30, 34, 36, 36, 38, 30, 32, 32, 34, 20, 22, 22, 24], "width": 142, "height": 142}}, "Spiral": {"Spiral": {"width": 235, "height": 237}}}}], "animations": {"t0_IDLE": {"bones": {"Spiral": {"rotate": [{}, {"time": 2, "value": -360}], "scale": [{"curve": [0.333, 1, 0.667, 1.059, 0.333, 1, 0.667, 1.059]}, {"time": 1, "x": 1.059, "y": 1.059, "curve": [1.333, 1.059, 1.667, 1, 1.333, 1.059, 1.667, 1]}, {"time": 2}]}}}, "t1_IDLE": {"bones": {"Circles": {"rotate": [{}, {"time": 3.3333, "value": -360}], "translate": [{"x": -3.48, "y": -6.38, "curve": [0.456, -4.1, 0.911, -5.96, 0.446, -6.3, 0.915, -2.75]}, {"time": 1.3667, "x": -5.96, "y": -2.75, "curve": [1.933, -5.96, 2.5, -3.28, 1.933, -2.75, 2.513, -3.83]}, {"time": 3.0667, "x": -3.28, "y": -5.96, "curve": [3.156, -3.28, 3.245, -3.36, 3.155, -6.3, 3.245, -6.4]}, {"time": 3.3333, "x": -3.48, "y": -6.38}], "shear": [{"y": -0.3, "curve": [0.293, 0, 0.58, 0, 0.293, 2.28, 0.58, 5]}, {"time": 0.8667, "y": 5, "curve": [1.422, 0, 1.978, 0, 1.422, 5, 1.978, -5]}, {"time": 2.5333, "y": -5, "curve": [2.802, 0, 3.07, 0, 2.802, -5, 3.07, -2.71]}, {"time": 3.3333, "y": -0.3}]}, "cntr": {"scale": [{"x": 0.978, "y": 0.978, "curve": [0.556, 0.978, 1.111, 1.051, 0.556, 0.978, 1.111, 1.051]}, {"time": 1.6667, "x": 1.051, "y": 1.051, "curve": [2.222, 1.051, 2.778, 0.978, 2.222, 1.051, 2.778, 0.978]}, {"time": 3.3333, "x": 0.978, "y": 0.978}]}}}, "t2_IDLE": {"slots": {"Circle0": {"rgba": [{"color": "431377ff"}]}, "Circle1": {"rgba": [{"color": "720ec500"}, {"time": 0.1333, "color": "720ec5ff"}, {"time": 1.3333, "color": "431377ff"}]}, "Circle2": {"rgba": [{"color": "571198ff"}, {"time": 0.5, "color": "431377ff", "curve": "stepped"}, {"time": 0.5333, "color": "720ec500"}, {"time": 0.6667, "color": "720ec5ff"}, {"time": 1.3333, "color": "571198ff"}]}, "Circle3": {"rgba": [{"color": "6a0fb8ff"}, {"time": 0.9667, "color": "431377ff", "curve": "stepped"}, {"time": 1, "color": "720ec500"}, {"time": 1.1333, "color": "720ec5ff"}, {"time": 1.3333, "color": "6a0fb8ff"}]}, "Circle_edge0": {"rgba": [{"color": "832fa6ff"}]}, "Circle_edge1": {"rgba": [{"color": "bd48da00"}, {"time": 0.1333, "color": "bd48daff"}, {"time": 1.3333, "color": "832fa6ff"}]}, "Circle_edge2": {"rgba": [{"color": "9b39bcff"}, {"time": 0.5, "color": "832fa6ff", "curve": "stepped"}, {"time": 0.5333, "color": "bd48da00"}, {"time": 0.6667, "color": "bd48daff"}, {"time": 1.3333, "color": "9b39bcff"}]}, "Circle_edge3": {"rgba": [{"color": "b344d1ff"}, {"time": 0.9667, "color": "832fa6ff", "curve": "stepped"}, {"time": 1, "color": "bd48da00"}, {"time": 1.1333, "color": "bd48daff"}, {"time": 1.3333, "color": "b344d1ff"}]}}, "bones": {"Circle_1": {"translate": [{"curve": [0.408, 17.52, 0.919, -19.93, 0.47, -14.6, 0.889, 21.75]}, {"time": 1.3333, "y": 21.75}], "scale": [{"x": 1.089, "y": 1.089}, {"time": 1.3333, "x": 0.513, "y": 0.513}]}, "Circle_edge1": {"scale": [{"x": 1.008, "y": 1.008}, {"time": 1.3333, "x": 0.903, "y": 0.903}]}, "Circle_2": {"translate": [{"x": -3.99, "y": 10.17, "curve": [0.174, -6.76, 0.344, -7.51, 0.169, 16.13, 0.334, 21.75]}, {"time": 0.5, "y": 21.75, "curve": "stepped"}, {"time": 0.5333, "curve": [0.778, 10.48, 1.06, 0.41, 0.801, -7.81, 1.07, 0.57]}, {"time": 1.3333, "x": -3.99, "y": 10.17}], "scale": [{"x": 0.729, "y": 0.729}, {"time": 0.5, "x": 0.513, "y": 0.513, "curve": "stepped"}, {"time": 0.5333, "x": 1.089, "y": 1.089}, {"time": 1.3333, "x": 0.729, "y": 0.729}]}, "Circle_edge2": {"scale": [{"x": 0.943, "y": 0.943}, {"time": 0.5, "x": 0.903, "y": 0.903, "curve": "stepped"}, {"time": 0.5333, "x": 1.008, "y": 1.008}, {"time": 1.3333, "x": 0.943, "y": 0.943}]}, "Circle_3": {"translate": [{"x": 4.32, "y": -0.75, "curve": [0.331, 1.77, 0.663, -14.62, 0.375, 3.16, 0.621, 21.75]}, {"time": 0.9667, "y": 21.75, "curve": "stepped"}, {"time": 1, "curve": [1.106, 4.55, 1.219, 5.22, 1.123, -1.85, 1.234, -1.84]}, {"time": 1.3333, "x": 4.32, "y": -0.75}], "scale": [{"x": 0.945, "y": 0.945}, {"time": 0.9667, "x": 0.513, "y": 0.513, "curve": "stepped"}, {"time": 1, "x": 1.089, "y": 1.089}, {"time": 1.3333, "x": 0.945, "y": 0.945}]}, "Circle_edge3": {"scale": [{"x": 0.982, "y": 0.982}, {"time": 0.9667, "x": 0.903, "y": 0.903, "curve": "stepped"}, {"time": 1, "x": 1.008, "y": 1.008}, {"time": 1.3333, "x": 0.982, "y": 0.982}]}, "Circle_0": {"translate": [{"y": 21.75}], "scale": [{"x": 0.513, "y": 0.513}]}, "Circle_edge0": {"scale": [{"x": 0.903, "y": 0.903}]}}, "drawOrder": [{"offsets": [{"slot": "Circle_edge3", "offset": 5}, {"slot": "Circle_edge2", "offset": 2}, {"slot": "Circle1", "offset": -4}, {"slot": "Circle_edge1", "offset": 2}]}, {"time": 0.5333, "offsets": [{"slot": "Circle3", "offset": 2}, {"slot": "Circle_edge3", "offset": 4}, {"slot": "Circle_edge2", "offset": 4}, {"slot": "Circle_edge1", "offset": 1}]}, {"time": 1, "offsets": [{"slot": "Circle_edge3", "offset": 6}, {"slot": "Circle_edge2", "offset": 3}, {"slot": "Circle_edge1", "offset": 0}]}]}}}