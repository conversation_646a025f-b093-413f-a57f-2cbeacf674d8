import { CollectableItemBase } from '@/game/core/CoreGameplay/Collectables/CollectableItemBase.ts'
import type { CollectableOptions } from '@/game/core/CoreGameplay/Collectables/CollectableOptions.ts'
import { CoinSkins } from '@/game/core/CoreGameplay/Collectables/TokenCollectable.ts'
import type { SpineGameObject, Vector2 } from '@esotericsoftware/spine-phaser'
import { DepthOrder } from '../Constants/DephOrdering'
import { CoinAnimations, MobDataSetKeys } from '../Player/PlayerStates/States/SpineAnimations'

export class CustomTokenCollectable extends CollectableItemBase {
  private spine: SpineGameObject | null = null
  private availableSkins: Set<string> = new Set(Object.values(CoinSkins))
  private skinId: CoinSkins = CoinSkins.Gem

  constructor(options: CollectableOptions) {
    super(options)
    this.createSpine(this.sprite.x, this.sprite.y)
    this.skinId = options.coinType === 2 ? CoinSkins.Gem : CoinSkins.Soon
    this.customCoinType = options.coinType || 0
    this.setSkin(this.skinId)
    this.spine?.setOrigin(0, 0)
    this.startIdleAnimation()
    this.platformOffsetX = 25
  }

  override reset(options: CollectableOptions): void {
    super.reset(options)
    this.platformOffsetX = 25
    console.log('coinType', options.coinType)
    this.skinId = options.coinType === 2 ? CoinSkins.Gem : CoinSkins.Soon
    this.customCoinType = options.coinType || 0
    this.setSkin(this.skinId)
    if (this.spine) {
      this.spine?.setVisible(true)
      this.spine?.setOrigin(0, 0)
      this.spine?.setPosition(
        this.sprite.x + CoinAnimations.X_OFFSET,
        this.sprite.y + CoinAnimations.Y_OFFSET
      )
    }
    this.startIdleAnimation()
  }

  override returnToPool(): void {
    if (this.spine) {
      this.spine?.setVisible(false)
      this.spine?.animationState.clearTracks()
    }
    super.returnToPool()
  }

  override destroyCollectable(): void {
    this.spine?.destroy()
    this.spine = null
    super.destroyCollectable()
  }

  private setSkin(skinName: CoinSkins) {
    if (this.availableSkins.has(skinName)) {
      this.spine?.skeleton.setSkinByName(skinName)

      this.spine?.skeleton.setSlotsToSetupPose()

      this.spine?.animationState.apply(this.spine?.skeleton)
    } else {
      console.warn(`Skin ${skinName} not found`)
    }
  }

  override collect(playerPos: Vector2) {
    super.collect(playerPos)
    this.spine?.animationState.setAnimation(0, CoinAnimations.T0_DISAPPEAR, false)
    this.returnToPoolWithDelay()
  }

  private createSpine(x: number, y: number) {
    this.spine = this.scene!.add.spine(
      x + CoinAnimations.X_OFFSET,
      y + CoinAnimations.Y_OFFSET,
      MobDataSetKeys.TON_COIN_DATA,
      MobDataSetKeys.TON_COIN_ATLAS
    )

    const finalScale = CoinAnimations.SCALE
    this.spine?.setScale(finalScale)

    this.sprite.setAlpha(0)
    this.spine?.setDepth(DepthOrder.Collectables)
    this.spine.x = this.staticBody.x
  }

  override syncPosition(x: number) {
    super.syncPosition(x)
    this.spine!.x = x + CoinAnimations.X_OFFSET + this.platformOffsetX
  }

  override syncSpineAndColliderPosition() {
    this.spine!.x = this.sprite.x + CoinAnimations.X_OFFSET
    this.spine!.y = this.sprite.y + CoinAnimations.Y_OFFSET
  }

  private startIdleAnimation() {
    this.spine?.animationState.setAnimation(0, CoinAnimations.T0_IDLE, true)
    this.spine?.animationState.setAnimation(1, CoinAnimations.T1_IDLE_R, true)
  }
}
