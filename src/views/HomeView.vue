<script setup lang="ts">
import { useFadeTransitionAnimation } from '@/composables/useFadeTransitionAnimation'
import { MIN_TRANSITION_ANIMATION_DELAY } from '@/constants'
import { hapticsService } from '@/shared/haptics/hapticsService.ts'
import { formatNumberToShortString } from '@/utils/number'
import { computed, onMounted, ref, useTemplateRef } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'

import platform from '@/assets/sprites/environment/platforms/static.png'
import { DEFAULT_SKIN_ID, SKIN_ID_TO_IMAGE } from '@/constants/skins'

import ClanEventBanners from '@/components/events/ClanEventBanners.vue'
import HotrecordBanners from '@/components/events/HotrecordBanners.vue'
import OnePercentBanners from '@/components/events/OnePercentBanners.vue'
import TonBanners from '@/components/events/TonBanners.vue'
import FooterMenu from '@/components/FooterMenu.vue'
import GyroscopeAccess from '@/components/GyroscopeAccess.vue'
import TicketsFarming from '@/components/home/<USER>'
import BalanceItem from '@/components/UI/BalanceItem.vue'
import CountdownTimer from '@/components/UI/CountdownTimer.vue'

import DeepDiveOfferWrapper from '@/components/banners/deep-dive/DeepDiveOfferWrapper.vue'
import ExploitersBannersWrapper from '@/components/banners/exploiters/ExploitersBannersWrapper.vue'
import LootBoxOfferBanner from '@/components/banners/LootBoxOfferBanner.vue'
import ScrollingOfferWrapper from '@/components/banners/scrolling-offer/ScrollingOfferWrapper.vue'
import SkinForTonBanner from '@/components/banners/SkinForTonBanner.vue'
import SnakeOfferWrapper from '@/components/banners/snake-offer/SnakeOfferWrapper.vue'
import BoosterSelector from '@/components/BoosterSelector.vue'
import DailyRewardDialog from '@/components/daily-rewards/DailyRewardDialog.vue'
import CustomCoinBannersWithSkinAndLootBox from '@/components/events/custom-coin/CustomCoinBannersWithSkinAndLootBox.vue'
import CommunityBattleBanners from '@/components/events/battle-event/CommunityBattleBanners.vue'
import TutorialDialog from '@/components/game/TutorialDialog.vue'
import LeagueUserProgress from '@/components/leagues/LeagueUserProgress.vue'
import LivesModal from '@/components/lives/LivesModal.vue'
import SettingsModal from '@/components/SettingsModal.vue'
import SubscriptionDialog from '@/components/SubscriptionDialog.vue'
import AvatarItem from '@/components/UI/AvatarItem.vue'
import CountdownTimerManual from '@/components/UI/CountdownTimerManual.vue'
import LeagueBlocker from '@/components/UI/LeagueBlocker.vue'
import NewBadge from '@/components/UI/NewBadge.vue'
import RedDotBadge from '@/components/UI/RedDotBadge.vue'
import VButton from '@/components/UI/VButton.vue'
import {
  useDeepDiveEventInfo,
  useHotrecordEventInfo,
  useOnePercentEventInfo,
  useScrollingEventInfo,
  useSnakeEventInfo,
  useFragmentEventInfo,
  useTonOnPlatformEventInfo
} from '@/composables/useEventInfo'
import { useWheelSpinStore } from '@/stores/wheelSpinStore.ts'
import { useWindowQueue } from '@/composables/useWindowQueue'
import { useAchievementsList } from '@/services/client/useAchievements.ts'
import { usePlayerState } from '@/services/client/usePlayerState'
import { useShopItems } from '@/services/client/useShopItems.ts'
import leaguesService from '@/services/local/leagues'
import type { StackableBoosterType } from '@/services/openapi'
import { cachedAchievementsData } from '@/shared/storage/cachedAchievementsData.ts'
import { cloudStorageService } from '@/shared/storage/cloudStorageService.ts'
import { useClanEventStore } from '@/stores/clanEventStore.ts'
import { useCustomCoinStore } from '@/stores/customCoinStore.ts'
import {
  useClanEventRankStore,
  useCustomCoinRankStore,
  useHotrecordRankStore,
  useOnepercentRankStore
} from '@/stores/eventRankStore'
import { useExploitersStore } from '@/stores/exploitersStore.ts'
import { useGyroscopeStore } from '@/stores/gyroscopeStore'
import {
  useHasUserSeenLootBoxOffer,
  useHasUserSeenMagicOffer,
  useHasUserSeenMagicRainbow,
  useHasUserSeenSnakeOffer,
  useHasUserSeenWheelSpin,
  useHasUserSeenFragment,
} from '@/stores/hasUserSeenStore.ts'
import { useLifesStore } from '@/stores/livesStore'
import { waitForGameAssetsLoaded } from '@/utils/game'
import { isVersionNewerThan } from '@/utils/telegram-sdk'
import ControlMethodSelector from '@/components/windows/ControlMethodSelector.vue'
import { useControlMethodStore } from '@/stores/controlMethodStore'
import FragmentOfferWrapper from '@/components/banners/fragment/FragmentOfferWrapper.vue'
import { useCommunityBattleStore } from '@/stores/communityBattleStore'
import { useSkinForTon } from '@/composables/useSkinForTon'

const isDev = __DEV__

const HAS_SEEN_SUBSCRIPTION_DIALOG = 'hasSeenSubscriptionDialog'
const HAS_SEEN_DAILY_REWARD = 'hasSeenDailyReward'

const router = useRouter()
const { t } = useI18n()

const livesStore = useLifesStore()
const { runTransitionAnimation } = useFadeTransitionAnimation()

const { playerState } = usePlayerState()
const { achievementsList } = useAchievementsList()
const userLeagueNumber = computed(() => playerState.value!.leagueLevel ?? 1)
const onepercentRankStore = useOnepercentRankStore()
const hotrecordRankStore = useHotrecordRankStore()
const customCoinRankStore = useCustomCoinRankStore()
const customCoinStore = useCustomCoinStore()
const clanEventRankStore = useClanEventRankStore()
const clanEventStore = useClanEventStore()
const exploitersStore = useExploitersStore()
const controlMethodStore = useControlMethodStore()
const communityBattleStore = useCommunityBattleStore()

const isOpenBoosterSelector = ref(false)
const controlSelectorRef = useTemplateRef('controlSelectorRef')
const isOpenSettingsModal = ref(false)
const isOpenLivesModal = ref(false)
const canSeeFarming = computed(() => {
  return (playerState.value!.tickets ?? 0) > 0
})

const subscribedToChannel = computed(() => {
  return playerState!.value!.subscribedToChannel ?? false
})

const { isTonSkinPurchased } = useSkinForTon()

const gyroscopeRequestStore = useGyroscopeStore()
const isOpenGyroscopeAccess = ref(false)

const tryStartGameStepOne = () => {
  controlSelectorRef.value?.openWindowWithCheck()
}

const tryStartGameStepTwo = () => {
  if (controlMethodStore.isGyroscope && !isVersionNewerThan('8.0') && gyroscopeRequestStore.shouldShowGyroscopeRequest) {
    isOpenGyroscopeAccess.value = true
  } else if (livesStore.lives <= 0) {
    isOpenLivesModal.value = true
  } else if (playerState.value?.tutorialMobs && !isShootTutorialDialogOpen.value) {
    isShootTutorialDialogOpen.value = true
  } else if (isMoveTutorialDialogOpen.value || isShootTutorialDialogOpen.value) {
    isMoveTutorialDialogOpen.value = false
    isShootTutorialDialogOpen.value = false
    startGame()
  } else {
    isOpenBoosterSelector.value = true
  }
}

const startGame = (activeBoosters: StackableBoosterType[] = []) => {
  cachedAchievementsData.playerAchievements = achievementsList?.value
  runTransitionAnimation()
  setTimeout(() => {
    const isTutorial = !!playerState.value?.tutorial
    waitForGameAssetsLoaded().then(() => {
      router.push({
        path: '/game',
        query: {
          tutorial: isTutorial ? '1' : '',
          boosters: activeBoosters.join(',')
        }
      })
    })
  }, MIN_TRANSITION_ANIMATION_DELAY)
}

const userSkin = computed(() => playerState.value?.skin ?? DEFAULT_SKIN_ID)

const onMenuClick = () => {
  hapticsService.triggerImpactHapticEvent('light')
}

const {
  isEventTimeOver: isOnepercentEventTimeOver,
  isEventActive: isOnepercentEventActive,
  eventTimeLeftInSeconds: onepercentTimeLeftInSeconds
} = useOnePercentEventInfo(playerState)
let onepercentCountdown = 0
const {
  isEventTimeOver: isHotrecordEventTimeOver,
  isEventActive: isHotrecordEventActive,
  eventTimeLeftInSeconds: hotrecordTimeLeftInSeconds
} = useHotrecordEventInfo(playerState)
let hotrecordCountdown = 0
const {
  isEventTimeOver: isTonEventTimeOver,
  isEventActive: isTonEventActive,
  eventTimeLeftInSeconds: tonTimeLeftInSeconds
} = useTonOnPlatformEventInfo(playerState)
const {
  isEventActive: isDeepDiveActive,
  buttonImage: deepDiveButtonImage,
  days: deepDiveDays,
  hours: deepDiveHours,
  minutes: deepDiveMinutes,
  seconds: deepDiveSeconds
} = useDeepDiveEventInfo()
const {
  isEventActive: isFragmentActive,
  days: fragmentDays,
  hours: fragmentHours,
  minutes: fragmentMinutes,
  seconds: fragmentSeconds
} = useFragmentEventInfo()
const {
  isEventActive: isScrollingEventActive,
  buttonImage: scrollingButtonImage,
  days: scrollingDays,
  hours: scrollingHours,
  minutes: scrollingMinutes,
  seconds: scrollingSeconds
} = useScrollingEventInfo()
const {
  isEventActive: isSnakeEventActive,
  buttonImage: snakeButtonImage,
  days: snakeDays,
  hours: snakeHours,
  minutes: snakeMinutes,
  seconds: snakeSeconds
} = useSnakeEventInfo()

const { shopItems } = useShopItems()
const isLootBoxOfferActive = computed(() =>
  shopItems.value?.lootboxes.find(l => l.lootboxType === 'throneLootBox')
)

const wheelSpinStore = useWheelSpinStore()

const hasUserSeenWheelSpin = useHasUserSeenWheelSpin()
const hasUserSeenMagicRainbow = useHasUserSeenMagicRainbow()
const hasUserSeenFragment = useHasUserSeenFragment()
const hasUserSeenMagicOffer = useHasUserSeenMagicOffer()
const hasUserSeenSnakeOffer = useHasUserSeenSnakeOffer()
const hasUserSeenLootBoxOffer = useHasUserSeenLootBoxOffer()

const isTonEventBanner = ref(false)
const isOnepercentEventBanner = ref(false)
const isCustomCoinBanner = ref(false)
const isBattleEventBanner = ref(false)
const isOpenDeepDive = ref(false)
const isOpenFragment = ref(false)
const isOpenScrollingOffer = ref(false)
const isOpenSnakeOffer = ref(false)
const isOpenSkinForBanner = ref(false)
const isOpenLootBoxBanner = ref(false)
const isExploitersBanner = ref(false)
const isMoveTutorialDialogOpen = ref(false)
const isShootTutorialDialogOpen = ref(false)

const onOnepercentEventClick = () => {
  if (playerState!.value!.onepercentEvent?.hasMetRequirement ?? false) {
    router.push({
      path: '/onepercent-event',
      query: { time: onepercentCountdown }
    })
  } else {
    isOnepercentEventBanner.value = true
  }
}

const onHotrecordEventClick = () => {
  router.push({
    path: '/hotrecord-event',
    query: { time: hotrecordCountdown }
  })
}

const onClanEventClick = () => {
  router.push({
    path: '/clan-event',
    query: { time: clanEventStore.countdown }
  })
}

const onCustomCoinEventClick = () => {
  if (
    playerState.value?.customCoinEvent?.hasLeaderboard &&
    playerState.value?.customCoinEvent?.hasMetRequirement &&
    customCoinStore.isCustomCoinPromoTimeOver
  ) {
    router.push({
      name: 'custom-coin-event'
    })
  } else {
    isCustomCoinBanner.value = true
  }
}

const onBattleEventClick = () => {
  if (
    playerState.value?.battleEvent?.hasLeaderboard &&
    playerState.value?.battleEvent?.hasMetRequirement
  ) {
    router.push({
      name: 'battle-event'
    })
  } else {
    isBattleEventBanner.value = true
  }
}

const {
  isOpen: isOpenDailyRewardInQueue,
  openWindowInQueue: openDailyWindowInQueue,
  closeWindowInQueue: closeDailyWindowInQueue
} = useWindowQueue('daily-reward-dialog')
const {
  isOpen: isOpenSubscriptionDialog,
  openWindowInQueue: openSubscriptionWindowInQueue,
  closeWindowInQueue: closeSubscriptionWindowInQueue
} = useWindowQueue('subscription-dialog')
const isOpenDailyReward = ref(false)

const checkDailyReward = async () => {
  if (playerState.value!.tutorial) return
  const hasAccessToFeature = leaguesService.hasAccess(userLeagueNumber.value, 'dailyReward')
  const hasSeenDailyReward = sessionStorage.getItem(HAS_SEEN_DAILY_REWARD)
  if (!hasAccessToFeature || !playerState.value?.dailyRewards || hasSeenDailyReward) return
  /** enable if some day would have blockers */
  // const now = await getNow()
  // const dailyRewardOpenDate = localStorageService.load<number>('dailyRewardLastOpenDate')
  // const isSameDay = dailyRewardOpenDate !== null && getCurrentDayEnd(now) === getCurrentDayEnd(dailyRewardOpenDate)
  // if (isSameDay) return
  const currentDay = playerState.value!.dailyRewards.currentDay
  const day = playerState.value!.dailyRewards.rewards.find(reward => reward.day === currentDay)!
  if (day && !day.claimed) {
    openDailyWindowInQueue()
  }
}

const closeDailyReward = async () => {
  closeDailyWindowInQueue()
  isOpenDailyReward.value = false
  sessionStorage.setItem(HAS_SEEN_DAILY_REWARD, '1')
  /** enable if some day would have blockers */
  // const now = await getNow()
  // localStorageService.save('dailyRewardLastOpenDate', now)
}

const checkSubscription = () => {
  cloudStorageService.load<string>(HAS_SEEN_SUBSCRIPTION_DIALOG).then(date => {
    if (!date && subscribedToChannel.value) return
    else if (date && subscribedToChannel.value) {
      cloudStorageService.delete(HAS_SEEN_SUBSCRIPTION_DIALOG)
      return
    }
    const today = new Date().toISOString().split('T')[0]
    if (date !== today) {
      openSubscriptionWindowInQueue()
    }
  })
}

const closeSubscriptionDialog = async () => {
  const today = new Date().toISOString().split('T')[0]
  closeSubscriptionWindowInQueue()
  cloudStorageService.save(HAS_SEEN_SUBSCRIPTION_DIALOG, today)
}

const checkEventsRank = () => {
  if (playerState.value?.onepercentEvent) {
    onepercentRankStore.getLastRank()
  }
  if (playerState.value?.hotrecordEvent) {
    hotrecordRankStore.getLastRank()
  }
  if (playerState.value?.clanEventReward) {
    clanEventRankStore.getLastRank()
  }
  if (
    playerState.value?.customCoinEvent?.hasLeaderboard &&
    customCoinStore.isCustomCoinPromoTimeOver
  ) {
    customCoinRankStore.getLastRank()
  }
}

onMounted(async () => {
  if (playerState.value?.tutorial) {
    isMoveTutorialDialogOpen.value = true
  }
  checkEventsRank()
  await checkDailyReward()
  await checkSubscription()
})
</script>

<template>
  <div class="view-container home-view flex flex-col !overflow-visible">
    <div class="flex-1 relative flex flex-col items-center">
      <div class="home-view__middle-block flex flex-col items-center">
        <RouterLink to="/menu/leagues" class="mb-[24px]" @click="onMenuClick">
          <LeagueUserProgress :userLeagueNumber="userLeagueNumber" />
        </RouterLink>
        <div class="flex items-center">
          <BalanceItem
            class="home-view__ticket z-10"
            image-class="home-view__ticket-image"
            bar-class="home-view__ticket-bar"
            balance-class="home-view__ticket-balance text-shadow text-shadow_black text-shadow_thin"
            iconName="ticket-bg"
          >
            {{ formatNumberToShortString(playerState!.tickets ?? 0) }}
          </BalanceItem>
          <BalanceItem
            class="home-view__multiplier z-10"
            image-class="home-view__multiplier-image"
            balance-class="home-view__multiplier-balance"
            bar-class="home-view__multiplier-bar"
            iconName="multiplier-bg"
            gold
          >
            {{ formatNumberToShortString(playerState!.multiplier ?? 1) }}
          </BalanceItem>
        </div>
        <TicketsFarming v-if="canSeeFarming" class="relative -top-[13px]" />
      </div>

      <div class="float-buttons float-buttons_left">
        <!-- Settings button -->
        <button class="top-menu-button" @click="isOpenSettingsModal = true">
          <AvatarItem class="top-menu-button__image" :league="userLeagueNumber" />
        </button>

        <!-- One percent event button -->
        <LeagueBlocker
          v-if="isOnepercentEventActive"
          class="top-menu-button"
          :userLeague="userLeagueNumber"
          feature="onePercentEvent"
          placement="right"
          @click="(onMenuClick(), onOnepercentEventClick())"
        >
          <img
            class="top-menu-button__image"
            src="@/assets/images/temp/onepercent/button.png"
            alt="event"
          />
          <div
            v-if="onepercentTimeLeftInSeconds > 0"
            class="top-menu-button__timer flex items-center gap-x-px"
          >
            <img class="w-[10px]" src="@/assets/images/temp/clock-icon.png" alt="clock" />
            <CountdownTimer
              timer-id="onepercentEvent"
              class="text-[10px] leading-[14px] text-[#1E4073]"
              :total-seconds="onepercentTimeLeftInSeconds"
              @countdown-finished="() => (isOnepercentEventTimeOver = true)"
              @tick="countdown => (onepercentCountdown = countdown)"
            />
          </div>
          <RedDotBadge v-if="onepercentRankStore.isRankChanged" class="top-[0px] -right-[6px]" />
        </LeagueBlocker>

        <!-- Hot record event button -->
        <LeagueBlocker
          v-if="isHotrecordEventActive"
          :userLeague="userLeagueNumber"
          class="top-menu-button"
          feature="hotRecordEvent"
          placement="right"
          @click="(onMenuClick(), onHotrecordEventClick())"
        >
          <img
            class="top-menu-button__image"
            src="@/assets/images/temp/hotrecord/button.png"
            alt="event"
          />
          <div
            v-if="hotrecordTimeLeftInSeconds > 0"
            class="top-menu-button__timer flex items-center gap-x-px"
          >
            <img class="w-[10px]" src="@/assets/images/temp/clock-icon.png" alt="clock" />
            <CountdownTimer
              timer-id="hotrecordEvent"
              class="text-[10px] leading-[14px] text-[#1E4073]"
              :total-seconds="hotrecordTimeLeftInSeconds"
              @countdown-finished="() => (isHotrecordEventTimeOver = true)"
              @tick="countdown => (hotrecordCountdown = countdown)"
            />
          </div>
          <RedDotBadge v-if="hotrecordRankStore.isRankChanged" class="top-[0px] -right-[6px]" />
        </LeagueBlocker>

        <!--Ton event button -->
        <LeagueBlocker
          v-if="isTonEventActive"
          :userLeague="userLeagueNumber"
          class="top-menu-button"
          feature="tonMiningEvent"
          placement="right"
          @click="(onMenuClick(), (isTonEventBanner = true))"
        >
          <img
            class="top-menu-button__image"
            src="@/assets/images/temp/ton-event/button.png"
            alt="event"
          />
          <div
            v-if="tonTimeLeftInSeconds > 0"
            class="top-menu-button__timer flex items-center gap-x-px"
          >
            <img class="w-[10px]" src="@/assets/images/temp/clock-icon.png" alt="clock" />
            <CountdownTimer
              timer-id="tonEvent"
              class="text-[10px] leading-[14px] text-[#1E4073]"
              :total-seconds="tonTimeLeftInSeconds"
              @countdown-finished="() => (isTonEventTimeOver = true)"
            />
          </div>
        </LeagueBlocker>

        <!-- Custom coin event button -->
        <LeagueBlocker
          v-if="customCoinStore.isCustomCoinActive"
          :userLeague="userLeagueNumber"
          class="top-menu-button"
          feature="customCoinEvent"
          placement="right"
          @click="(onMenuClick(), onCustomCoinEventClick())"
        >
          <img
            class="top-menu-button__image"
            src="../assets/images/temp/custom-coin-event/button.png"
            alt="event"
          />
          <div class="top-menu-button__timer flex items-center gap-x-px">
            <img class="w-[10px]" src="@/assets/images/temp/clock-icon.png" alt="clock" />
            <CountdownTimerManual
              class="text-[10px] leading-[14px] text-[#1E4073]"
              :days="customCoinStore.days"
              :hours="customCoinStore.hours"
              :minutes="customCoinStore.minutes"
              :seconds="customCoinStore.seconds"
            />
          </div>
        </LeagueBlocker>

        <!-- Battle event button -->
        <LeagueBlocker
          v-if="communityBattleStore.isCommunityBattleActive"
          :userLeague="userLeagueNumber"
          class="top-menu-button"
          feature="battleEvent"
          placement="right"
          @click="(onMenuClick(), onBattleEventClick())"
        >
          <img
            class="top-menu-button__image"
            src="@/assets/images/temp/battle-event/button.png"
            alt="event"
          />
          <div class="top-menu-button__timer flex items-center gap-x-px">
            <img class="w-[10px]" src="@/assets/images/temp/clock-icon.png" alt="clock" />
            <CountdownTimerManual
              class="text-[10px] leading-[14px] text-[#1E4073]"
              :days="communityBattleStore.days"
              :hours="communityBattleStore.hours"
              :minutes="communityBattleStore.minutes"
              :seconds="communityBattleStore.seconds"
            />
          </div>
        </LeagueBlocker>
        <!-- ClanEvent event button -->
        <div
          v-if="clanEventStore.isClanEventActive"
          class="top-menu-button"
          @click="(onMenuClick(), onClanEventClick())"
        >
          <img
            class="top-menu-button__image"
            src="@/assets/images/temp/onepercent/button.png"
            alt="event"
          />
          <p class="absolute bottom-[5px] text-shadow text-shadow_black text-white z-10">
            Clan Event
          </p>
          <div class="top-menu-button__timer flex items-center gap-x-px">
            <img class="w-[10px]" src="@/assets/images/temp/clock-icon.png" alt="clock" />
            <CountdownTimerManual
              class="text-[10px] leading-[14px] text-[#1E4073]"
              :days="clanEventStore.days"
              :hours="clanEventStore.hours"
              :minutes="clanEventStore.minutes"
              :seconds="clanEventStore.seconds"
            />
          </div>
        </div>

        <!-- Daily reword button -->
        <LeagueBlocker
          v-if="isDev"
          class="top-menu-button"
          :userLeague="userLeagueNumber"
          feature="dailyReward"
          placement="left"
          @click="() => (onMenuClick, (isOpenDailyReward = true))"
        >
          <img
            class="top-menu-button__image"
            src="@/assets/images/temp/daily-rewards/button.png"
            alt="daily-reward-icon"
          />
        </LeagueBlocker>

        <!-- Wallet button -->
        <RouterLink
          v-if="isDev"
          to="/test-page"
          class="top-menu-button"
          @click="onMenuClick"
        >
          <img
            class="top-menu-button__image"
            src="@/assets/images/temp/wallet-icon.png"
            alt="wallet icon"
          />
          <span class="top-menu-button__text text-shadow text-shadow_black">Test</span>
        </RouterLink>
      </div>

      <div class="float-buttons float-buttons_right">
        <!-- Skin for ton button -->
        <div
          v-if="!isTonSkinPurchased && leaguesService.hasAccess(userLeagueNumber, 'offers')"
          class="top-menu-button"
          @click="() => (onMenuClick(), (isOpenSkinForBanner = true))"
        >
          <img
            class="top-menu-button__image"
            src="@/assets/images/temp/banners/skin-for-ton/button.png"
            alt="skin for ton icon"
          />
        </div>

        <!-- Fragment button -->
        <LeagueBlocker
          v-if="isFragmentActive"
          class="top-menu-button"
          :userLeague="userLeagueNumber"
          feature="puzzleCoins"
          placement="left"
          @click="
            (hasUserSeenFragment.markFragmentAsSeen(),
            onMenuClick(),
            (isOpenFragment = true))
          "
        >
          <img
            class="top-menu-button__image"
            src="@/assets/images/temp/fragment/button.png"
            alt="fragment offer icon"
          />
          <NewBadge
            v-if="!hasUserSeenFragment.hasSeenFragment"
            class="-top-[10px] -right-[10px]"
          />
          <RedDotBadge
            v-else-if="playerState?.puzzleCoins"
            class="marker_with-text -top-[13px] -right-[6px]"
          >
            {{ playerState?.puzzleCoins }}
          </RedDotBadge>
          <div class="top-menu-button__timer flex items-center gap-x-px">
            <img class="w-[10px]" src="@/assets/images/temp/clock-icon.png" alt="clock" />
            <CountdownTimerManual
              class="text-[10px] leading-[14px] text-[#1E4073]"
              :days="fragmentDays"
              :hours="fragmentHours"
              :minutes="fragmentMinutes"
              :seconds="fragmentSeconds"
            />
          </div>
        </LeagueBlocker>

        <!-- Magic rainbow button -->
        <LeagueBlocker
          v-if="isDeepDiveActive"
          class="top-menu-button"
          :userLeague="userLeagueNumber"
          feature="dynamicCoins"
          placement="left"
          @click="
            (hasUserSeenMagicRainbow.markMagicRainbowAsSeen(),
            onMenuClick(),
            (isOpenDeepDive = true))
          "
        >
          <img
            class="top-menu-button__image"
            :src="deepDiveButtonImage"
            alt="deep dive offer icon"
          />
          <NewBadge
            v-if="!hasUserSeenMagicRainbow.hasSeenMagicRainbow"
            class="-top-[10px] -right-[10px]"
          />
          <RedDotBadge
            v-else-if="playerState?.dynamicCoins"
            class="marker_with-text -top-[13px] -right-[6px]"
          >
            {{ playerState?.dynamicCoins }}
          </RedDotBadge>
          <div class="top-menu-button__timer flex items-center gap-x-px">
            <img class="w-[10px]" src="@/assets/images/temp/clock-icon.png" alt="clock" />
            <CountdownTimerManual
              class="text-[10px] leading-[14px] text-[#1E4073]"
              :days="deepDiveDays"
              :hours="deepDiveHours"
              :minutes="deepDiveMinutes"
              :seconds="deepDiveSeconds"
            />
          </div>
        </LeagueBlocker>

        <!-- Magic offer button -->
        <div
          v-if="isScrollingEventActive && leaguesService.hasAccess(userLeagueNumber, 'offers')"
          class="top-menu-button"
          @click="
            () => (
              hasUserSeenMagicOffer.markMagicOfferAsSeen(),
              onMenuClick(),
              (isOpenScrollingOffer = true)
            )
          "
        >
          <img
            class="top-menu-button__image"
            :src="scrollingButtonImage"
            alt="scrolling offer icon"
          />
          <NewBadge
            v-if="!hasUserSeenMagicOffer.hasSeenMagicOffer"
            class="-top-[10px] -right-[10px]"
          />
          <div class="top-menu-button__timer flex items-center gap-x-px">
            <img class="w-[10px]" src="@/assets/images/temp/clock-icon.png" alt="clock" />
            <CountdownTimerManual
              class="text-[10px] leading-[14px] text-[#1E4073]"
              :days="scrollingDays"
              :hours="scrollingHours"
              :minutes="scrollingMinutes"
              :seconds="scrollingSeconds"
            />
          </div>
        </div>

        <!-- Snake offer button -->
        <div
          v-if="isSnakeEventActive && leaguesService.hasAccess(userLeagueNumber, 'offers')"
          class="top-menu-button"
          @click="
            () => (
              hasUserSeenSnakeOffer.markSnakeOfferAsSeen(), onMenuClick(), (isOpenSnakeOffer = true)
            )
          "
        >
          <img class="top-menu-button__image" :src="snakeButtonImage" alt="snake offer icon" />
          <NewBadge
            v-if="!hasUserSeenSnakeOffer.hasSeenSnakeOffer"
            class="-top-[10px] -right-[10px]"
          />
          <div class="top-menu-button__timer flex items-center gap-x-px">
            <img class="w-[10px]" src="@/assets/images/temp/clock-icon.png" alt="clock" />
            <CountdownTimerManual
              class="text-[10px] leading-[14px] text-[#1E4073]"
              :days="snakeDays"
              :hours="snakeHours"
              :minutes="snakeMinutes"
              :seconds="snakeSeconds"
            />
          </div>
        </div>

        <!-- Exploiters heist button -->
        <div
          v-if="exploitersStore.isExploitersHeistActive"
          class="top-menu-button"
          @click="(onMenuClick(), (isExploitersBanner = true))"
        >
          <img
            class="top-menu-button__image"
            src="@/assets/images/temp/banners/exploiters/button.png"
            alt="event"
          />
          <p class="absolute bottom-[5px] text-shadow text-shadow_black text-white z-10">
            {{ t('exploiters.heist') }}
          </p>
          <div
            class="top-menu-button__timer !bottom-[8px] text-center h-[18px] w-[52px] bg-[#CA0000] rounded-[6px]"
          >
            <CountdownTimerManual
              class="text-[10px] leading-[14px] text-white"
              :days="exploitersStore.days"
              :hours="exploitersStore.hours"
              :minutes="exploitersStore.minutes"
              :seconds="exploitersStore.seconds"
            />
          </div>
        </div>

        <!-- Loot box offer button -->
        <div
          v-if="isLootBoxOfferActive && leaguesService.hasAccess(userLeagueNumber, 'offers')"
          class="top-menu-button"
          @click="
            () => (
              onMenuClick(),
              hasUserSeenLootBoxOffer.markLootBoxOfferAsSeen(),
              (isOpenLootBoxBanner = true)
            )
          "
        >
          <img
            class="top-menu-button__image"
            src="@/assets/images/temp/banners/loot-box-offer/button.png"
            alt="star wars offer icon"
          />
          <NewBadge
            v-if="!hasUserSeenLootBoxOffer.hasSeenLootBoxOffer"
            class="-top-[10px] -right-[10px]"
          />
        </div>
      </div>

      <!-- Clan button -->
      <RouterLink
        v-if="isDev"
        to="/menu/clan"
        class="top-menu-button !absolute bottom-[3%] left-[14px]"
        @click="onMenuClick"
      >
        <img
          src="@/assets/images/temp/clan-badge.png"
          class="top-menu-button__image"
          alt="clan icon"
        />
      </RouterLink>

      <!-- Play button / uni animation -->
      <div class="absolute flex flex-col items-center justify-center bottom-[3%]">
        <div class="menu-animation">
          <div class="relative">
            <img class="menu-animation__uni" :src="SKIN_ID_TO_IMAGE[userSkin]" alt="uni" />
            <img class="menu-animation__platform" :src="platform" alt="platform" />
          </div>
        </div>

        <VButton
          type="accent"
          :text="t('actions.play')"
          class="mx-auto min-w-[150px]"
          @click="tryStartGameStepOne"
          size="medium"
        />
      </div>

      <!-- Wheel spin button -->
      <div
        v-if="leaguesService.hasAccess(userLeagueNumber, 'offers')"
        class="top-menu-button !absolute bottom-[2.7%] right-[14px]"
        @click="
          (hasUserSeenWheelSpin.markWheelSpinAsSeen(), onMenuClick, router.push('/menu/wheel-spin'))
        "
      >
        <img
          class="top-menu-button__image"
          src="@/assets/images/temp/wheel-spin/button.png"
          alt="wheel spin"
        />
        <NewBadge v-if="!hasUserSeenWheelSpin.hasSeenWheelSpin" class="-top-[10px] -right-[10px]" />
        <RedDotBadge
          v-else-if="wheelSpinStore.availableWheelSpinsCount"
          class="marker_with-text -top-[13px] -right-[6px]"
        >
          {{ wheelSpinStore.availableWheelSpinsCount }}
        </RedDotBadge>
      </div>
    </div>
    <FooterMenu />
    <RouterView v-slot="{ Component }">
      <Transition :name="'route-animation'">
        <component :is="Component" />
      </Transition>
    </RouterView>
    <GyroscopeAccess
      :isOpen="isOpenGyroscopeAccess"
      @granted="tryStartGameStepTwo"
      @close="isOpenGyroscopeAccess = false"
    />
    <OnePercentBanners
      :isOpen="isOnepercentEventBanner"
      :onepercentTimeLeftInSeconds="onepercentTimeLeftInSeconds"
      @close="() => (isOnepercentEventBanner = false)"
      @go-to-event="onOnepercentEventClick"
    />
    <HotrecordBanners @go-to-event="onHotrecordEventClick" />
    <ClanEventBanners @go-to-event="onClanEventClick" />
    <TonBanners
      :isOpen="isTonEventBanner"
      :tonTimeLeftInSeconds="tonTimeLeftInSeconds"
      @close="() => (isTonEventBanner = false)"
      @startGame="tryStartGameStepOne"
    />
    <CustomCoinBannersWithSkinAndLootBox
      :is-open="isCustomCoinBanner"
      @close="isCustomCoinBanner = false"
    />
    <CommunityBattleBanners
      :is-open="isBattleEventBanner"
      @close="isBattleEventBanner = false"
    />
    <TutorialDialog
      :open-move="isMoveTutorialDialogOpen"
      :open-shoot="isShootTutorialDialogOpen"
      @continue="() => startGame()"
    />
  </div>
  <LivesModal :isOpen="isOpenLivesModal" @close="() => (isOpenLivesModal = false)" />
  <SettingsModal :is-open="isOpenSettingsModal" @close="isOpenSettingsModal = false" />
  <DailyRewardDialog
    v-if="isOpenDailyRewardInQueue || isOpenDailyReward"
    @close="closeDailyReward"
  />
  <SubscriptionDialog :is-open="isOpenSubscriptionDialog" @close="closeSubscriptionDialog" />
  <FragmentOfferWrapper
    v-if="isFragmentActive"
    :isOpen="isOpenFragment"
    :days="fragmentDays"
    :hours="fragmentHours"
    :minutes="fragmentMinutes"
    :seconds="fragmentSeconds"
    @close="isOpenFragment = false"
  />
  <DeepDiveOfferWrapper
    v-if="isDeepDiveActive"
    :isOpen="isOpenDeepDive"
    :days="deepDiveDays"
    :hours="deepDiveHours"
    :minutes="deepDiveMinutes"
    :seconds="deepDiveSeconds"
    @close="isOpenDeepDive = false"
  />
  <ScrollingOfferWrapper
    v-if="isScrollingEventActive"
    :isOpen="isOpenScrollingOffer"
    :days="scrollingDays"
    :hours="scrollingHours"
    :minutes="scrollingMinutes"
    :seconds="scrollingSeconds"
    @close="isOpenScrollingOffer = false"
  />
  <SnakeOfferWrapper
    v-if="isSnakeEventActive"
    :isOpen="isOpenSnakeOffer"
    :days="snakeDays"
    :hours="snakeHours"
    :minutes="snakeMinutes"
    :seconds="snakeSeconds"
    @close="isOpenSnakeOffer = false"
  />
  <ExploitersBannersWrapper
    :isOpen="isExploitersBanner"
    @startGame="tryStartGameStepOne"
    @close="() => (isExploitersBanner = false)"
  />
  <LootBoxOfferBanner
    v-if="isLootBoxOfferActive"
    :isOpen="isOpenLootBoxBanner"
    @close="isOpenLootBoxBanner = false"
  />
  <SkinForTonBanner
    v-if="!isTonSkinPurchased"
    :isOpen="isOpenSkinForBanner"
    @close="isOpenSkinForBanner = false"
  />
  <BoosterSelector
    v-if="isOpenBoosterSelector"
    @close="isOpenBoosterSelector = false"
    @start-game="startGame"
  />
  <ControlMethodSelector
    ref="controlSelectorRef"
    @already-selected="tryStartGameStepTwo"
    @close="tryStartGameStepTwo"
  />
</template>

<style lang="scss">
.home-view {
  --top-padding: 5px;

  &__middle-block {
    position: relative;
    z-index: 0;
    top: calc(var(--top-padding) + 8px);
    width: fit-content;
    margin: 0 auto;
  }

  &__ticket {
    &-image {
      width: 34px;
      height: 34px;
    }

    &-bar {
      height: 24px;
      padding: 0 14px 0 23px;

      &:after {
        border-radius: 0;
      }
    }

    &-balance {
      font-size: 17px;
    }
  }

  &__multiplier {
    &-image {
      left: 5px;
      width: 29px;
      height: 29px;
    }

    &-bar {
      height: 24px;
      padding: 0 10px 0 22px;
    }

    &-balance {
      font-size: 17px;
    }
  }
}

.float-buttons {
  position: absolute;
  top: var(--top-padding);
  z-index: 1;
  width: 50%;
  padding: 0 14px;
  // overflow: visible;

  display: flex;
  flex-direction: column;
  row-gap: 4%;
  height: calc(100% - var(--top-padding));

  pointer-events: none;

  &_left {
    left: 0;
    align-items: start;
  }

  &_right {
    right: 0;
    align-items: end;
  }
}

.top-menu-button {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  row-gap: 4px;

  width: 48px;
  height: 48px;

  pointer-events: auto;

  &__image {
    position: absolute;
    object-fit: contain;
    width: 100%;
    height: 100%;
  }

  &__timer {
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translate(-50%, 100%);
  }

  &__text {
    position: absolute;
    bottom: -6px;
    left: 50%;
    transform: translateX(-50%);

    font-size: 16px;
    line-height: 22px;
  }
}

.menu-animation {
  font-size: 2rem;
  color: white;

  @keyframes smoothbounce {
    from {
      transform: translate3d(0, 0, 0);
    }
    to {
      transform: translate3d(0, -170px, 0);
    }
  }

  &__uni {
    width: 70px;
    position: relative;
    left: 0;
    top: -6px;

    animation: smoothbounce 0.5s;
    animation-direction: alternate;
    animation-iteration-count: infinite;
  }

  &__platform {
    width: 70px;
    position: relative;
    top: -10px;
  }
}

.route-animation-enter-active,
.route-animation-leave-active {
  transition: opacity 0.2s ease;
}

.route-animation-enter-from,
.route-animation-leave-to {
  opacity: 0;
}
</style>
