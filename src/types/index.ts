import type {
  ExternalCurrencyType,
  InGameCurrencyType,
  Price,
  RewardType,
  StackableBoosterType
} from '@/services/openapi'

export type InGamePrice = {
  amount: number
  currency: InGameCurrencyType
}

export type ExternalPrice = {
  amount: number
  currency: ExternalCurrencyType
}

export type TimeBoundBoosterType = 'timeBoundMagneticField' | 'timeBoundAimbot' | 'timeBoundJumper'

export type ShopScrollTarget = 'lootbox' | 'wheelSpins' | 'friends' | 'hard' | 'soft'
export type MissionsScrollTarget = 'partners'

/* NUMERAL REWARD */
export type PlayerBalanceRewardType =
  | 'tickets'
  | 'hard'
  | 'soft'
  | 'refsFake'
  | 'ton'
  | 'magicHorns'
  | 'dynamicCoins'
  | 'puzzleCoins'
  | 'customCoin'
  | 'battleCoin'
  | 'lives'

export type ObjectRewardType = 'wheelSpins'

export type NumeralRewardType = Extract<
  RewardType,
  PlayerBalanceRewardType | StackableBoosterType | ObjectRewardType
>

export type NumeralRewardInfo = {
  type: NumeralRewardType
  value: number
}

export const isNumeralRewardType = (r: RewardType): r is NumeralRewardType => {
  const obj: Record<NumeralRewardType, boolean> = {
    tickets: true,
    hard: true,
    soft: true,
    refsFake: true,
    ton: true,
    magicHorns: true,
    dynamicCoins: true,
    puzzleCoins: true,
    stackableMagneticField: true,
    stackableAimbot: true,
    stackableJumper: true,
    lives: true,
    customCoin: true,
    battleCoin: true,
    wheelSpins: true
  }
  return obj[r as NumeralRewardType]
}

export const isStackableRewardType = (r: RewardType): r is StackableBoosterType => {
  const obj: Record<StackableBoosterType, boolean> = {
    stackableAimbot: true,
    stackableJumper: true,
    stackableMagneticField: true
  }
  return obj[r as StackableBoosterType]
}

export const isObjectRewardType = (r: RewardType): r is ObjectRewardType => {
  const obj: Record<ObjectRewardType, boolean> = {
    wheelSpins: true
  }
  return obj[r as ObjectRewardType]
}

export const isNumeralReward = (r: Reward): r is NumeralReward => {
  return isNumeralRewardType(r.type)
}

/* TIME REWARD */
export type TimeRewardType = Extract<
  RewardType,
  'unlimitedLives' | 'timeBoundMagneticField' | 'timeBoundAimbot' | 'timeBoundJumper'
>

export type TimeRewardInfo = {
  type: TimeRewardType
  value: number
}

export const isTimeRewardType = (r: RewardType): r is TimeRewardType => {
  const obj: Record<TimeRewardType, boolean> = {
    unlimitedLives: true,
    timeBoundMagneticField: true,
    timeBoundAimbot: true,
    timeBoundJumper: true
  }
  return obj[r as TimeRewardType]
}

export const isTimeReward = (r: Reward): r is TimeReward => {
  return isTimeRewardType(r.type as RewardType)
}

/* SIMPLE REWARD */
export type SimpleRewardType = Extract<RewardType, 'fullLives'>

export type SimpleRewardInfo = {
  type: SimpleRewardType
  value: number
}

export const isSimpleRewardType = (r: RewardType): r is SimpleRewardType => {
  return r === 'fullLives'
}

export const isSimpleReward = (r: Reward): r is SimpleReward => {
  return isSimpleRewardType(r.type)
}

export type RewardScreenItemType = SimpleRewardType | NumeralRewardType | TimeRewardType

/* LOOTBOX REWARD */
export type LootboxRewardType = Extract<
  RewardType,
  | 'rainbowLootBox'
  | 'luckyLootBox'
  | 'cryptoLootBox'
  | 'moonLootBox'
  | 'easterLootBox'
  | 'minecraftLootBox'
  | 'bananaLootBox'
  | 'starWarsLootBox'
  | 'infinityLootBox'
  | 'kungFuLootBox'
  | 'spongeLootBox'
  | 'shrekLootBox'
  | 'justiceLootBox'
  | 'scoobyLootBox'
  | 'combatLootBox'
  | 'giftLootBox'
  | 'madagascarLootBox'
  | 'hustleLootBox'
  | 'throneLootBox'
>

export type LootboxRewardInfo = {
  type: LootboxRewardType
  value: number
}

export const isLootboxRewardType = (r: RewardType): r is LootboxRewardType => {
  const obj: Record<LootboxRewardType, boolean> = {
    rainbowLootBox: true,
    luckyLootBox: true,
    cryptoLootBox: true,
    moonLootBox: true,
    easterLootBox: true,
    minecraftLootBox: true,
    bananaLootBox: true,
    starWarsLootBox: true,
    infinityLootBox: true,
    kungFuLootBox: true,
    spongeLootBox: true,
    shrekLootBox: true,
    justiceLootBox: true,
    scoobyLootBox: true,
    combatLootBox: true,
    giftLootBox: true,
    madagascarLootBox: true,
    hustleLootBox: true,
    throneLootBox: true
  }
  return obj[r as LootboxRewardType]
}

/* SKIN REWARD */
export type SkinRewardType = Extract<RewardType, 'skin'>

export type SkinRewardInfo = {
  type: SkinRewardType
  value: number
  multiplier: number
}

export const isSkinRewardType = (r: RewardType): r is SkinRewardType => {
  const obj: Record<SkinRewardType, boolean> = {
    skin: true
  }
  return obj[r as SkinRewardType]
}

type NumeralReward = {
  type: NumeralRewardType
  value: number
  prevValue: number
}
type TimeReward = {
  type: TimeRewardType
  duration: number
}
type SimpleReward = {
  type: SimpleRewardType
}
export type Reward = NumeralReward | TimeReward | SimpleReward
export type SkinReward = {
  skinId: number
  multiplier: number
  plusMultiplier: number
}

export const isInGameCurrencyType = (type: string): type is InGameCurrencyType => {
  return !['stars', 'usd', 'ton'].includes(type)
}

export const isInGamePrice = (price: Price): price is InGamePrice => {
  return isInGameCurrencyType(price.currency)
}

export const isExternalCurrencyType = (type: string): type is ExternalCurrencyType => {
  return ['stars', 'usd', 'ton'].includes(type)
}

export const isExternalPrice = (price: Price): price is ExternalPrice => {
  return isExternalCurrencyType(price.currency)
}

export type TaskRewardType = Exclude<
  RewardType,
  | 'refsFake'
  | 'fullLives'
  | 'ton'
  | 'skin'
  | 'rainbowLootBox'
  | 'luckyLootBox'
  | 'cryptoLootBox'
  | 'moonLootBox'
  | 'easterLootBox'
  | 'minecraftLootBox'
  | 'bananaLootBox'
  | 'starWarsLootBox'
  | 'infinityLootBox'
  | 'kungFuLootBox'
  | 'spongeLootBox'
  | 'shrekLootBox'
  | 'justiceLootBox'
  | 'customCoin'
  | 'battleCoin'
  | 'lives'
  | 'wheelSpins'
>
